#!/usr/bin/env python3
"""
Test script for Firecrawl hosted MCP server integration.

This script tests the enhanced MCP server's ability to connect to and use
Firecrawl's hosted MCP service.
"""

import asyncio
import os
import sys
import json
import logging

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from multi_mcp_client import MultiMCPClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_firecrawl_hosted():
    """Test the Firecrawl hosted MCP server connection and tool calls."""
    
    # Check if API key is available
    api_key = os.getenv('FIRECRAWL_API_KEY')
    if not api_key:
        print("❌ FIRECRAWL_API_KEY environment variable not set")
        return False
    
    print(f"✅ Found FIRECRAWL_API_KEY: {api_key[:10]}...")
    
    # Create multi-MCP client
    client = MultiMCPClient()
    
    try:
        # Connect to Firecrawl hosted service
        print("\n🔗 Connecting to Firecrawl hosted MCP server...")
        server_url = f"https://mcp.firecrawl.dev/{api_key}/sse"
        
        success = await client.add_server(
            server_id="firecrawl-hosted-test",
            server_url=server_url,
            protocol="sse",
            description="Firecrawl hosted MCP server (test)"
        )
        
        if not success:
            print("❌ Failed to connect to Firecrawl hosted service")
            return False
        
        print("✅ Successfully connected to Firecrawl hosted service")
        
        # List available tools
        servers = client.list_servers()
        if "firecrawl-hosted-test" in servers:
            tools = servers["firecrawl-hosted-test"]["tools"]
            print(f"📋 Available tools: {', '.join(tools)}")
        
        # Test firecrawl_search
        print("\n🔍 Testing firecrawl_search...")
        search_result = await client.call_tool(
            server_id="firecrawl-hosted-test",
            tool_name="firecrawl_search",
            tool_input={
                "query": "artificial intelligence news",
                "limit": 3
            },
            tool_call_id="test_search"
        )
        
        if search_result.get('success'):
            print("✅ firecrawl_search succeeded")
            content = search_result.get('content', [])
            if isinstance(content, list) and len(content) > 0:
                print(f"📄 Found {len(content)} search results")
                # Print first result summary
                first_result = content[0]
                if hasattr(first_result, 'text'):
                    text = first_result.text[:200] + "..." if len(first_result.text) > 200 else first_result.text
                    print(f"📝 First result preview: {text}")
            else:
                print(f"⚠️  Unexpected content format: {type(content)}")
        else:
            print(f"❌ firecrawl_search failed: {search_result.get('error')}")
            return False
        
        # Test firecrawl_scrape
        print("\n🕷️  Testing firecrawl_scrape...")
        scrape_result = await client.call_tool(
            server_id="firecrawl-hosted-test",
            tool_name="firecrawl_scrape",
            tool_input={
                "url": "https://example.com",
                "formats": ["markdown"],
                "onlyMainContent": True
            },
            tool_call_id="test_scrape"
        )
        
        if scrape_result.get('success'):
            print("✅ firecrawl_scrape succeeded")
            content = scrape_result.get('content', [])
            if isinstance(content, list) and len(content) > 0:
                first_result = content[0]
                if hasattr(first_result, 'text'):
                    text = first_result.text[:200] + "..." if len(first_result.text) > 200 else first_result.text
                    print(f"📝 Scraped content preview: {text}")
            else:
                print(f"⚠️  Unexpected content format: {type(content)}")
        else:
            print(f"❌ firecrawl_scrape failed: {scrape_result.get('error')}")
            return False
        
        print("\n🎉 All tests passed! Firecrawl hosted MCP server is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        try:
            await client.cleanup()
            print("✅ Cleanup completed")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

async def main():
    """Main test function."""
    print("🧪 Testing Firecrawl Hosted MCP Server Integration")
    print("=" * 50)
    
    success = await test_firecrawl_hosted()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
