#!/usr/bin/env python3
"""
Test script for the revised MCP server.
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from mcp_http_server_revised import MCPServerRevised


async def test_server():
    """Test the revised server functionality."""
    print("Testing MCP Server Revised...")
    
    # Create server instance
    server = MCPServerRevised("server_config.json")
    
    # Test initialization
    try:
        await server.initialize()
        print("✅ Server initialization successful")
    except Exception as e:
        print(f"❌ Server initialization failed: {e}")
        return False
    
    # Test cleanup
    try:
        await server.cleanup()
        print("✅ Server cleanup successful")
    except Exception as e:
        print(f"❌ Server cleanup failed: {e}")
        return False
    
    print("🎉 All tests passed!")
    return True


def main():
    """Main test function."""
    success = asyncio.run(test_server())
    if success:
        print("\n✅ Revised server is ready to use!")
        print("Start it with: python mcp_http_server_revised.py --port 9000")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
