#!/usr/bin/env python3
"""
Revised MCP HTTP Server - Clean and organized implementation

Features:
- Local and third-party MCP server support
- URL and NPX style server configurations
- SSE and HTTP streaming endpoints
- Parameter mapping and default parameters from config
- Simple, organized code structure
"""

import os
import sys
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from mcp.server.fastmcp import FastMCP, Context

# Import multi-MCP client
try:
    from multi_mcp_client import MultiMCPClient
except ImportError:
    print("Error: multi_mcp_client not found. Please ensure it's in the same directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MCPServerRevised:
    """Revised MCP server with clean architecture."""
    
    def __init__(self, config_file: str = "server_config.json"):
        self.config_file = config_file
        self.mcp = FastMCP("mcp_server_revised")
        self.third_party_client = MultiMCPClient()
        self.server_configs: Dict[str, Dict[str, Any]] = {}
        
        # Register local tools
        self._register_local_tools()
    
    def _register_local_tools(self):
        """Register local MCP tools."""
        
        @self.mcp.tool()
        async def echo(message: str) -> str:
            """Echo a message back."""
            return f"Echo: {message}"
        
        @self.mcp.tool()
        async def server_status() -> str:
            """Get server status."""
            status = "MCP Server Revised - Status\n"
            status += "=" * 30 + "\n"
            status += f"Local tools: Available\n"
            status += f"Third-party servers: {len(self.server_configs)}\n"
            
            for server_id, config in self.server_configs.items():
                enabled = config.get('enabled', True)
                server_type = 'URL' if 'url' in config else 'NPX'
                status += f"  - {server_id}: {server_type} ({'Enabled' if enabled else 'Disabled'})\n"
            
            return status
        
        @self.mcp.tool()
        async def list_tools() -> str:
            """List all available tools."""
            tools = "Available Tools\n"
            tools += "=" * 20 + "\n"
            tools += "Local tools:\n"
            tools += "  - echo\n"
            tools += "  - server_status\n"
            tools += "  - list_tools\n"
            
            # Add third-party tools
            if hasattr(self.third_party_client, 'connections'):
                for server_id, connection in self.third_party_client.connections.items():
                    if connection and connection.get('tools'):
                        tools += f"\n{server_id} tools:\n"
                        for tool in connection['tools']:
                            namespace = self.server_configs.get(server_id, {}).get('namespace', server_id)
                            tool_name = f"{namespace}__{tool['name']}"
                            tools += f"  - {tool_name}\n"
            
            return tools
    
    async def load_config(self):
        """Load server configuration."""
        if not os.path.exists(self.config_file):
            logger.warning(f"Config file {self.config_file} not found")
            return
        
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            self.server_configs = config.get('mcpServers', {})
            logger.info(f"Loaded configuration for {len(self.server_configs)} servers")
            
        except Exception as e:
            logger.error(f"Error loading config: {e}")
    
    async def connect_third_party_servers(self):
        """Connect to all enabled third-party servers."""
        connected_count = 0
        
        for server_id, config in self.server_configs.items():
            if not config.get('enabled', True):
                logger.info(f"Skipping disabled server: {server_id}")
                continue
            
            try:
                success = await self._connect_server(server_id, config)
                if success:
                    connected_count += 1
                    await self._register_third_party_tools(server_id, config)
                    logger.info(f"Successfully connected to {server_id}")
                else:
                    logger.error(f"Failed to connect to {server_id}")
            
            except Exception as e:
                logger.error(f"Error connecting to {server_id}: {e}")
        
        logger.info(f"Connected to {connected_count}/{len([c for c in self.server_configs.values() if c.get('enabled', True)])} servers")
    
    async def _connect_server(self, server_id: str, config: Dict[str, Any]) -> bool:
        """Connect to a single third-party server."""
        
        if 'url' in config:
            # URL-based server (hosted)
            url = self._resolve_env_vars(config['url'])
            protocol = config.get('protocol', 'sse')
            description = config.get('description', f'{server_id} hosted server')
            
            return await self.third_party_client.add_server(
                server_id=server_id,
                server_url=url,
                protocol=protocol,
                description=description
            )
        
        elif 'command' in config:
            # Process-spawned server (NPX)
            command = config['command']
            args = config.get('args', [])
            env = {k: self._resolve_env_vars(v) for k, v in config.get('env', {}).items()}
            description = config.get('description', f'{server_id} process server')
            
            return await self.third_party_client.add_server_process(
                server_id=server_id,
                command=command,
                args=args,
                env=env,
                description=description
            )
        
        else:
            logger.error(f"Invalid config for {server_id}: no 'url' or 'command' specified")
            return False
    
    def _resolve_env_vars(self, value: str) -> str:
        """Resolve environment variables in configuration values."""
        if isinstance(value, str) and '{' in value and '}' in value:
            try:
                return value.format(**os.environ)
            except KeyError as e:
                logger.error(f"Environment variable not found: {e}")
                return value
        return value
    
    async def _register_third_party_tools(self, server_id: str, config: Dict[str, Any]):
        """Register tools from a third-party server."""
        connection = self.third_party_client.connections.get(server_id)
        if not connection or not connection.get('tools'):
            return
        
        namespace = config.get('namespace', server_id)
        param_mappings = config.get('parameterMapping', {})
        default_params = config.get('defaultParameters', {})
        
        for tool_info in connection['tools']:
            tool_name = tool_info['name']
            namespaced_name = f"{namespace}__{tool_name}"

            # Create the delegated tool function with closure
            def create_delegated_tool(srv_id, tl_name, param_map, default_par):
                async def delegated_tool(**kwargs):
                    # Apply parameter mappings
                    mapped_params = self._apply_parameter_mapping(
                        kwargs, param_map.get(tl_name, {}), default_par.get(tl_name, {})
                    )

                    # Call the third-party tool
                    result = await self._call_third_party_tool(srv_id, tl_name, mapped_params)

                    if result.get('success'):
                        return self._format_tool_result(result.get('content', []))
                    else:
                        raise Exception(f"Tool call failed: {result.get('error', 'Unknown error')}")

                # Set function metadata
                delegated_tool.__name__ = namespaced_name
                delegated_tool.__doc__ = tool_info.get('description', f'Delegated tool: {tl_name}')
                return delegated_tool

            # Create and register the tool
            delegated_func = create_delegated_tool(server_id, tool_name, param_mappings, default_params)
            self.mcp.tool()(delegated_func)

            logger.info(f"Registered delegated tool: {namespaced_name}")
    
    def _apply_parameter_mapping(self, params: Dict[str, Any], mappings: Dict[str, str], defaults: Dict[str, Any]) -> Dict[str, Any]:
        """Apply parameter mappings and defaults."""
        result = defaults.copy()
        
        for param_name, param_value in params.items():
            # Apply mapping if it exists
            mapped_name = mappings.get(param_name, param_name)
            result[mapped_name] = param_value
        
        return result
    
    async def _call_third_party_tool(self, server_id: str, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a third-party tool with error handling."""
        try:
            result = await asyncio.wait_for(
                self.third_party_client.call_tool(
                    server_id=server_id,
                    tool_name=tool_name,
                    tool_input=params,
                    tool_call_id=f"call_{server_id}_{tool_name}"
                ),
                timeout=60.0
            )
            return result
        
        except Exception as e:
            logger.error(f"Error calling {tool_name} on {server_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': None
            }
    
    def _format_tool_result(self, content: List[Any]) -> str:
        """Format tool result content for display."""
        if not content:
            return "No content returned"
        
        if len(content) == 1 and hasattr(content[0], 'text'):
            return content[0].text
        
        # Handle multiple content items
        result = ""
        for i, item in enumerate(content):
            if hasattr(item, 'text'):
                result += item.text
                if i < len(content) - 1:
                    result += "\n\n"
        
        return result or "Content format not supported"
    
    async def initialize(self):
        """Initialize the server."""
        logger.info("Initializing MCP server...")
        await self.load_config()
        await self.connect_third_party_servers()
        logger.info("MCP server initialization complete")
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.third_party_client:
            await self.third_party_client.cleanup()
    
    def start(self, host: str = "0.0.0.0", port: int = 9000):
        """Start the server."""
        logger.info(f"Starting MCP server on {host}:{port}")
        
        # Initialize in a separate event loop to avoid conflicts
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.initialize())
        finally:
            loop.close()
        
        # Start the main server
        try:
            app = self.mcp.streamable_http_app()
            import uvicorn
            uvicorn.run(app, host=host, port=port, log_level="info")
        except KeyboardInterrupt:
            logger.info("Server interrupted by user")
        finally:
            # Cleanup
            asyncio.run(self.cleanup())


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP HTTP Server Revised")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=9000, help="Port to bind to")
    parser.add_argument("--config", default="server_config.json", help="Configuration file")
    
    args = parser.parse_args()
    
    server = MCPServerRevised(args.config)
    server.start(args.host, args.port)


if __name__ == "__main__":
    main()
