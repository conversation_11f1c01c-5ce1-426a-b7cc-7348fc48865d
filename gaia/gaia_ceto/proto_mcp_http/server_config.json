{"description": "Enhanced MCP Server Configuration (Augment Pattern)", "mcpServers": {"firecrawl-mcp": {"enabled": false, "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"}, "namespace": "fc", "description": "Firecrawl MCP server spawned via npx", "parameterMapping": {"firecrawl_scrape": {}, "firecrawl_map": {}, "firecrawl_search": {"q": "query"}}, "defaultParameters": {"firecrawl_scrape": {"formats": ["markdown"], "onlyMainContent": true}}}, "exa-mcp": {"enabled": false, "command": "npx", "args": ["-y", "exa-mcp"], "env": {"EXA_API_KEY": "{EXA_API_KEY}"}, "namespace": "exa", "description": "Embedding-based web search with Exa"}, "brave-search-mcp": {"enabled": false, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_SEARCH_API_KEY": "{BRAVE_SEARCH_API_KEY}"}, "namespace": "search", "description": "Brave Search API for web and local search", "parameterMapping": {"brave_web_search": {"q": "query"}, "brave_local_search": {"q": "query"}}, "defaultParameters": {"brave_web_search": {"count": 10}, "brave_local_search": {"count": 5}}}, "firecrawl-hosted": {"enabled": true, "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse", "protocol": "sse", "namespace": "web", "description": "Firecrawl hosted MCP server", "parameterMapping": {"firecrawl_scrape": {}, "firecrawl_map": {}, "firecrawl_search": {"q": "query"}}, "defaultParameters": {"firecrawl_scrape": {"formats": ["markdown"], "onlyMainContent": true}}}}, "chat_term_usage": {"description": "Connect chat_term to this enhanced server", "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp", "available_tools": ["echostring (local)", "echostring_table (local)", "long_task (local)", "server_status (local)", "list_all_tools (local)", "third_party_health (local)", "fc_firecrawl_scrape (delegated)", "fs_read_file (delegated)"]}}