#!/usr/bin/env python3
"""
Enhanced MCP HTTP Server with Third-Party Delegation

This module implements an enhanced MCP server that provides its own tools
AND delegates to third-party MCP servers in a nested fashion. This allows
chat_term to connect to a single server on port 9000 but access tools from
multiple sources.

Features:
- Local tools (echostring, long_task, etc.)
- Third-party MCP server delegation (Firecrawl, etc.)
- Namespaced tool access (e.g., web.firecrawl_scrape)
- Configuration-driven backend setup
- Maintains HTTP streaming paradigm
"""

import asyncio
import json
import logging
import os
import signal
import sys
import subprocess
from typing import Dict, List, Optional, Any
from contextlib import AsyncExitStack

from mcp.server.fastmcp import FastMCP, Context
import argparse
import uvicorn

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

# Import shared tools from the common module
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    long_task,
    firecrawl_scrape_text_only,
)

# Import multi-MCP client for third-party delegation
try:
    # Try relative import first
    from .multi_mcp_client import MultiMCPClient
except ImportError:
    try:
        # Fall back to direct import
        from multi_mcp_client import MultiMCPClient
    except ImportError:
        print("Warning: multi_mcp_client not found. Third-party delegation will be disabled.")
        MultiMCPClient = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedMCPServer:
    """Enhanced MCP server with local tools and third-party delegation."""
    
    def __init__(self, config_file: str = "server_config.json"):
        """Initialize the enhanced MCP server.
        
        Args:
            config_file: Path to configuration file for third-party servers
        """
        self.config_file = config_file
        self.mcp = FastMCP("gaia_enhanced_mcp_server")
        self.third_party_client = MultiMCPClient() if MultiMCPClient else None
        self.third_party_tools: Dict[str, Dict[str, Any]] = {}

        # Store parameter mappings and default parameters from config
        self.parameter_mappings: Dict[str, Dict[str, Dict[str, str]]] = {}
        self.default_parameters: Dict[str, Dict[str, Dict[str, Any]]] = {}
        
        # Register local tools first
        self._register_local_tools()

    def _validate_parameter_mappings(self, server_id: str, server_config: Dict[str, Any]) -> bool:
        """Validate parameter mappings and default parameters in server configuration."""
        try:
            # Validate parameter mappings structure
            if 'parameterMapping' in server_config:
                param_mappings = server_config['parameterMapping']
                if not isinstance(param_mappings, dict):
                    logger.error(f"parameterMapping for {server_id} must be a dictionary")
                    return False

                for tool_name, mappings in param_mappings.items():
                    if not isinstance(mappings, dict):
                        logger.error(f"Parameter mappings for {tool_name} in {server_id} must be a dictionary")
                        return False

                    for old_param, new_param in mappings.items():
                        if not isinstance(old_param, str) or not isinstance(new_param, str):
                            logger.error(f"Parameter mapping keys and values must be strings in {server_id}.{tool_name}")
                            return False

            # Validate default parameters structure
            if 'defaultParameters' in server_config:
                default_params = server_config['defaultParameters']
                if not isinstance(default_params, dict):
                    logger.error(f"defaultParameters for {server_id} must be a dictionary")
                    return False

                for tool_name, defaults in default_params.items():
                    if not isinstance(defaults, dict):
                        logger.error(f"Default parameters for {tool_name} in {server_id} must be a dictionary")
                        return False

            return True

        except Exception as e:
            logger.error(f"Error validating parameter configuration for {server_id}: {e}")
            return False

    def _register_local_tools(self):
        """Register the local/native tools."""
        logger.info("Registering local tools...")
        
        # Register existing local tools
        self.mcp.add_tool(echostring)
        self.mcp.add_tool(echostring_table)
        self.mcp.add_tool(long_task)
        #self.mcp.add_tool(firecrawl_scrape_text_only)
        
        # Add server management tools
        self._add_server_management_tools()
        
        logger.info("Local tools registered successfully")
    
    def _add_server_management_tools(self):
        """Add server management and status tools."""
        
        @self.mcp.tool()
        async def server_status(ctx: Context) -> str:
            """Get status of the enhanced MCP server and all third-party connections."""
            result = "Enhanced MCP Server Status\n"
            result += "=" * 35 + "\n\n"
            
            # Local tools count
            local_tool_count = len([name for name in dir(self.mcp) if not name.startswith('_')])
            result += f"Local Tools: {local_tool_count}\n"
            
            # Third-party connections
            if self.third_party_client:
                connections = self.third_party_client.list_servers()
                result += f"Third-party Servers: {len(connections)}\n"
                result += f"Third-party Tools: {len(self.third_party_tools)}\n\n"
                
                for server_id, info in connections.items():
                    result += f"{server_id}:\n"
                    result += f"  URL: {info['url']}\n"
                    result += f"  Protocol: {info['protocol']}\n"
                    result += f"  Tools: {len(info['tools'])}\n"
                    result += f"  Connected: {info['connected_at']}\n\n"
            else:
                result += "Third-party delegation: Disabled\n"
            
            return result
        
        @self.mcp.tool()
        async def list_all_tools(ctx: Context) -> str:
            """List all available tools (local and third-party)."""
            result = "All Available Tools\n"
            result += "=" * 25 + "\n\n"
            
            result += "Local Tools:\n"
            result += "-" * 15 + "\n"
            # This is a simplified list - in practice you'd enumerate actual tools
            local_tools = ["echostring", "echostring_table", "long_task", "firecrawl_scrape_text_only", "server_status", "list_all_tools"]
            for tool in local_tools:
                result += f"  {tool}\n"
            
            if self.third_party_tools:
                result += "\nThird-party Tools:\n"
                result += "-" * 20 + "\n"
                for tool_name in sorted(self.third_party_tools.keys()):
                    server_id = self.third_party_tools[tool_name]['server_id']
                    result += f"  {tool_name} (from {server_id})\n"
            
            return result
        
        @self.mcp.tool()
        async def third_party_health(ctx: Context) -> str:
            """Check health of all third-party server connections."""
            if not self.third_party_client:
                return "Third-party delegation is disabled"
            
            result = "Third-party Server Health\n"
            result += "=" * 30 + "\n\n"
            
            servers = self.third_party_client.list_servers()
            for server_id, info in servers.items():
                try:
                    # Try a simple tool call to test connectivity
                    test_tools = ['echostring', 'health_check', 'ping']
                    test_tool = None
                    
                    for tool_name in test_tools:
                        if tool_name in info['tools']:
                            test_tool = tool_name
                            break
                    
                    if test_tool:
                        test_result = await self.third_party_client.call_tool(
                            server_id=server_id,
                            tool_name=test_tool,
                            tool_input="health_test",
                            tool_call_id="health_check"
                        )
                        status = "✓ Healthy" if test_result['success'] else f"✗ Error: {test_result['error']}"
                    else:
                        status = "? No test tool available"
                        
                except Exception as e:
                    status = f"✗ Connection error: {e}"
                
                result += f"{server_id}: {status}\n"
            
            return result
    
    async def load_third_party_servers(self) -> bool:
        """Load and spawn MCP servers from config file using Augment pattern."""
        if not self.third_party_client:
            logger.warning("Third-party client not available, skipping third-party server loading")
            return False

        if not os.path.exists(self.config_file):
            logger.info(f"No config file found at {self.config_file}, skipping third-party servers")
            return True  # Not an error, just no third-party servers

        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file: {e}")
            return False

        # Use Augment-style configuration format
        mcp_servers = config.get('mcpServers', {})
        if not mcp_servers:
            logger.info("No MCP servers defined in config")
            return True

        success_count = 0
        failed_servers = []

        for server_id, server_config in mcp_servers.items():
            if not server_config.get('enabled', True):
                logger.info(f"Skipping disabled MCP server: {server_id}")
                continue

            # Validate and store parameter mappings and default parameters from config
            if not self._validate_parameter_mappings(server_id, server_config):
                logger.error(f"Invalid parameter configuration for {server_id}, skipping...")
                failed_servers.append(server_id)
                continue

            if 'parameterMapping' in server_config:
                self.parameter_mappings[server_id] = server_config['parameterMapping']
                logger.info(f"Loaded parameter mappings for {server_id}: {server_config['parameterMapping']}")

            if 'defaultParameters' in server_config:
                self.default_parameters[server_id] = server_config['defaultParameters']
                logger.info(f"Loaded default parameters for {server_id}: {server_config['defaultParameters']}")

            namespace = server_config.get('namespace', server_id)

            # Check if this is a URL-based server or process-spawned server
            if 'url' in server_config:
                # URL-based server configuration
                url = server_config['url']
                protocol = server_config.get('protocol', 'sse')
                description = server_config.get('description', f'{server_id} hosted MCP server')

                try:
                    success = await self._connect_url_server(
                        server_id=server_id,
                        url=url,
                        protocol=protocol,
                        description=description,
                        namespace=namespace
                    )

                    if success:
                        success_count += 1
                    else:
                        failed_servers.append(server_id)

                except Exception as e:
                    logger.error(f"Failed to connect to URL-based MCP server {server_id}: {e}")
                    failed_servers.append(server_id)

            else:
                # Process-spawned server configuration
                command = server_config.get('command')
                args = server_config.get('args', [])
                env = server_config.get('env', {})

                if not command:
                    logger.error(f"No command specified for MCP server: {server_id}")
                    failed_servers.append(server_id)
                    continue

                # Spawn the MCP server process and connect to it
                try:
                    success = await self._spawn_and_connect_server(
                        server_id=server_id,
                        command=command,
                        args=args,
                        env=env,
                        namespace=namespace
                    )

                    if success:
                        success_count += 1
                    else:
                        failed_servers.append(server_id)

                except Exception as e:
                    logger.error(f"Failed to spawn/connect to MCP server {server_id}: {e}")
                    failed_servers.append(server_id)

        total_enabled = len([s for s in mcp_servers.values() if s.get('enabled', True)])
        logger.info(f"Connected to {success_count}/{total_enabled} MCP servers")

        if failed_servers:
            logger.warning(f"Failed to connect to servers: {', '.join(failed_servers)}")

        return success_count > 0 or total_enabled == 0

    async def _connect_url_server(self, server_id: str, url: str, protocol: str,
                                description: str, namespace: str) -> bool:
        """Connect to a URL-based MCP server."""
        try:
            # Handle environment variable substitution in URL
            if '{' in url and '}' in url:
                try:
                    resolved_url = url.format(**os.environ)
                except KeyError as e:
                    logger.error(f"Environment variable not found for {server_id}: {e}")
                    return False
            else:
                resolved_url = url

            logger.info(f"Connecting to URL-based MCP server {server_id} at: {resolved_url}")

            success = await self.third_party_client.add_server(
                server_id=server_id,
                server_url=resolved_url,
                protocol=protocol,
                description=description
            )

            if success:
                # Validate the connection by checking available tools
                connection_info = self.third_party_client.connections.get(server_id)
                if connection_info and connection_info.get('tools'):
                    tool_names = [tool['name'] for tool in connection_info['tools']]
                    logger.info(f"Successfully connected to {server_id} with {len(tool_names)} tools: {', '.join(tool_names[:5])}{'...' if len(tool_names) > 5 else ''}")
                    await self._register_third_party_tools(server_id, namespace)
                    return True
                else:
                    logger.error(f"Connected to {server_id} but no tools available")
                    return False
            else:
                logger.error(f"Failed to connect to URL-based MCP server {server_id}")
                return False

        except Exception as e:
            logger.error(f"Error connecting to URL-based server {server_id}: {e}")
            return False

    async def _spawn_and_connect_server(self, server_id: str, command: str,
                                      args: List[str], env: Dict[str, str],
                                      namespace: str) -> bool:
        """Spawn an MCP server process and connect to it using Augment pattern."""
        try:
            # Resolve environment variable templates in the env dict
            resolved_env = {}
            for key, value in env.items():
                if isinstance(value, str) and value.startswith('{') and value.endswith('}'):
                    # This is a template like {EXA_API_KEY}
                    env_var_name = value[1:-1]  # Remove { and }
                    resolved_value = os.getenv(env_var_name)
                    if resolved_value:
                        resolved_env[key] = resolved_value
                        logger.debug(f"Resolved {key}={value} to actual environment value")
                    else:
                        logger.error(f"Environment variable {env_var_name} not found for {key}")
                        return False
                else:
                    resolved_env[key] = value

            # Prepare environment variables
            process_env = os.environ.copy()
            process_env.update(resolved_env)

            logger.info(f"Spawning MCP server: {command} {' '.join(args)} with env keys: {list(resolved_env.keys())}")

            # For firecrawl-mcp, we know it's a stdio-based MCP server
            if server_id == 'firecrawl-mcp':
                api_key = resolved_env.get('FIRECRAWL_API_KEY')
                if not api_key:
                    logger.error("FIRECRAWL_API_KEY not found in resolved environment")
                    return False

                # TODO: Implement actual process spawning and stdio communication
                # For now, fall back to the hosted service until we implement stdio support
                logger.warning("Process spawning not yet implemented, falling back to hosted service")

                # Connect to the hosted Firecrawl MCP server as temporary fallback
                server_url = f"https://mcp.firecrawl.dev/{api_key}/sse"
                logger.info(f"Connecting to Firecrawl at: {server_url}")

                success = await self.third_party_client.add_server(
                    server_id=server_id,
                    server_url=server_url,
                    protocol='sse',
                    description='Firecrawl MCP server (hosted fallback)'
                )

                if success:
                    # Validate the connection by checking available tools
                    connection_info = self.third_party_client.connections.get(server_id)
                    if connection_info and connection_info.get('tools'):
                        tool_names = [tool['name'] for tool in connection_info['tools']]
                        logger.info(f"Successfully connected to {server_id} with {len(tool_names)} tools: {', '.join(tool_names[:5])}{'...' if len(tool_names) > 5 else ''}")
                        await self._register_third_party_tools(server_id, namespace)
                        return True
                    else:
                        logger.error(f"Connected to {server_id} but no tools available")
                        return False
                else:
                    logger.error(f"Failed to connect to hosted Firecrawl service for {server_id}")
                    return False

            # For other servers, implement generic process spawning
            logger.info(f"Implementing generic process spawning for {server_id}")

            # Use the multi_mcp_client to spawn the process
            if not self.third_party_client:
                logger.error("Third-party client not available for process spawning")
                return False

            success = await self.third_party_client.spawn_server(
                server_id=server_id,
                command=command,
                args=args,
                env=resolved_env,
                description=f"Spawned MCP server: {command} {' '.join(args)}"
            )

            if success:
                # Register the tools from the spawned server
                await self._register_third_party_tools(server_id, namespace)
                logger.info(f"Successfully spawned and registered {server_id}")
                return True
            else:
                logger.error(f"Failed to spawn {server_id}")
                return False

        except Exception as e:
            logger.error(f"Error spawning server {server_id}: {e}")
            return False

    async def _call_tool_with_retry(self, server_id: str, tool_name: str,
                                   tool_input: dict, tool_call_id: str,
                                   timeout_seconds: int, max_retries: int = 2) -> dict:
        """Call a third-party tool with retry logic for connection issues."""

        # Create a new event loop context to completely isolate the third-party call
        # This prevents the third-party MCP client from interfering with the main server's task groups
        import concurrent.futures
        import threading

        def run_isolated_call():
            """Run the tool call in a completely separate event loop."""
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                return loop.run_until_complete(self._isolated_tool_call(
                    server_id, tool_name, tool_input, tool_call_id, timeout_seconds, max_retries
                ))
            finally:
                loop.close()

        # Run the isolated call in a separate thread with its own event loop
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(run_isolated_call)
            try:
                # Wait for the result with a reasonable timeout
                result = future.result(timeout=timeout_seconds + 30)
                return result
            except concurrent.futures.TimeoutError:
                logger.error(f"Tool call {tool_name} timed out after {timeout_seconds + 30} seconds")
                return {
                    'success': False,
                    'error': f'Tool call timed out after {timeout_seconds + 30} seconds',
                    'content': None
                }
            except Exception as e:
                logger.error(f"Error in isolated tool call: {e}")
                return {
                    'success': False,
                    'error': f'Isolated tool call failed: {e}',
                    'content': None
                }

    async def _isolated_tool_call(self, server_id: str, tool_name: str,
                                 tool_input: dict, tool_call_id: str,
                                 timeout_seconds: int, max_retries: int) -> dict:
        """Execute the actual tool call in an isolated async context."""

        # Create a fresh multi-MCP client for this isolated call
        from multi_mcp_client import MultiMCPClient
        isolated_client = MultiMCPClient()

        try:
            # Reconnect to the specific server we need
            server_config = None
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                mcp_servers = config.get('mcpServers', {})
                server_config = mcp_servers.get(server_id)

            if not server_config:
                return {
                    'success': False,
                    'error': f'No configuration found for server {server_id}',
                    'content': None
                }

            # Connect to the server
            if 'url' in server_config:
                # URL-based server
                url = server_config['url']
                if '{' in url and '}' in url:
                    url = url.format(**os.environ)

                protocol = server_config.get('protocol', 'sse')
                description = server_config.get('description', f'{server_id} MCP server')

                success = await isolated_client.add_server(
                    server_id=server_id,
                    server_url=url,
                    protocol=protocol,
                    description=description
                )
            else:
                # Process-spawned server
                command = server_config.get('command')
                args = server_config.get('args', [])
                env = server_config.get('env', {})

                if not command:
                    return {
                        'success': False,
                        'error': f'No command specified for server {server_id}',
                        'content': None
                    }

                # Resolve environment variables in env
                resolved_env = {}
                for key, value in env.items():
                    if isinstance(value, str) and '{' in value and '}' in value:
                        try:
                            resolved_env[key] = value.format(**os.environ)
                        except KeyError as e:
                            return {
                                'success': False,
                                'error': f'Environment variable not found: {e}',
                                'content': None
                            }
                    else:
                        resolved_env[key] = value

                success = await isolated_client.add_server_process(
                    server_id=server_id,
                    command=command,
                    args=args,
                    env=resolved_env,
                    description=server_config.get('description', f'{server_id} MCP server')
                )

            if not success:
                return {
                    'success': False,
                    'error': f'Failed to connect to {server_id} in isolated mode',
                    'content': None
                }

            # Make the tool call
            for attempt in range(max_retries + 1):
                try:
                    logger.info(f"Isolated call {tool_name} on {server_id} (attempt {attempt + 1}/{max_retries + 1})")

                    result = await asyncio.wait_for(
                        isolated_client.call_tool(
                            server_id=server_id,
                            tool_name=tool_name,
                            tool_input=tool_input,
                            tool_call_id=tool_call_id
                        ),
                        timeout=timeout_seconds
                    )

                    if result.get('success', False):
                        return result

                    # If failed but not a connection error, don't retry
                    error_msg = result.get('error', '')
                    error_str = str(error_msg) if error_msg else ''
                    if 'ClosedResourceError' not in error_str and 'connection' not in error_str.lower():
                        return result

                    # Connection error - try once more if we have retries left
                    if attempt < max_retries:
                        logger.warning(f"Connection error on attempt {attempt + 1}, will retry...")
                        await asyncio.sleep(1.0)
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {tool_name}")
                        return result

                except Exception as e:
                    error_msg = str(e)
                    error_type = type(e).__name__

                    connection_errors = [
                        'ClosedResourceError', 'ConnectionError', 'ConnectionResetError',
                        'BrokenPipeError', 'TimeoutError', 'asyncio.TimeoutError'
                    ]

                    is_connection_error = (
                        any(err in error_type for err in connection_errors) or
                        any(err in error_msg for err in connection_errors) or
                        'connection' in error_msg.lower() or
                        'session' in error_msg.lower() or
                        'transport' in error_msg.lower()
                    )

                    if is_connection_error and attempt < max_retries:
                        logger.warning(f"Connection exception on attempt {attempt + 1} ({error_type}): {e}, will retry...")
                        await asyncio.sleep(1.0)
                    else:
                        logger.error(f"Tool call failed after {attempt + 1} attempts ({error_type}): {e}")
                        return {
                            'success': False,
                            'error': f'Tool call failed after {attempt + 1} attempts: {e}',
                            'content': None
                        }

            return {
                'success': False,
                'error': f'Unexpected error: all retry attempts exhausted',
                'content': None
            }

        finally:
            # Clean up the isolated client
            try:
                await isolated_client.cleanup()
            except Exception as e:
                logger.warning(f"Error cleaning up isolated client: {e}")

    async def _reconnect_server(self, server_id: str):
        """Reconnect to a third-party server with improved error handling."""
        try:
            logger.info(f"Attempting to reconnect to {server_id}...")

            # Load the original configuration to get reconnection details
            server_config = None
            if os.path.exists(self.config_file):
                try:
                    with open(self.config_file, 'r') as f:
                        config = json.load(f)
                    mcp_servers = config.get('mcpServers', {})
                    server_config = mcp_servers.get(server_id)
                except Exception as e:
                    logger.error(f"Error loading config for reconnection: {e}")

            if not server_config:
                logger.warning(f"No configuration found for {server_id}, cannot reconnect")
                return

            # Remove the old connection with better error handling
            if hasattr(self.third_party_client, 'connections') and server_id in self.third_party_client.connections:
                try:
                    # Give a short delay to let any pending operations complete
                    await asyncio.sleep(0.1)
                    await self.third_party_client.remove_server(server_id)
                except Exception as remove_error:
                    logger.warning(f"Error removing old connection for {server_id}: {remove_error} (proceeding anyway)")

            # Reconnect based on server type
            if 'url' in server_config:
                # URL-based server reconnection
                url = server_config['url']
                protocol = server_config.get('protocol', 'sse')
                description = server_config.get('description', f'{server_id} hosted MCP server (reconnected)')

                # Handle environment variable substitution in URL
                if '{' in url and '}' in url:
                    try:
                        resolved_url = url.format(**os.environ)
                    except KeyError as e:
                        logger.error(f"Environment variable not found for {server_id} reconnection: {e}")
                        return
                else:
                    resolved_url = url

                # Add the server again with retry logic
                for attempt in range(2):
                    try:
                        success = await self.third_party_client.add_server(
                            server_id=server_id,
                            server_url=resolved_url,
                            protocol=protocol,
                            description=description
                        )

                        if success:
                            logger.info(f"Successfully reconnected to {server_id}")
                            return
                        else:
                            logger.warning(f"Failed to reconnect to {server_id} on attempt {attempt + 1}")
                            if attempt == 0:
                                await asyncio.sleep(1.0)  # Wait before retry
                    except Exception as add_error:
                        logger.warning(f"Exception during reconnection attempt {attempt + 1}: {add_error}")
                        if attempt == 0:
                            await asyncio.sleep(1.0)  # Wait before retry

                logger.error(f"Failed to reconnect to {server_id} after 2 attempts")

            elif 'command' in server_config:
                # Process-spawned server reconnection
                command = server_config.get('command')
                args = server_config.get('args', [])
                env = server_config.get('env', {})
                namespace = server_config.get('namespace', server_id)

                try:
                    success = await self._spawn_and_connect_server(
                        server_id=server_id,
                        command=command,
                        args=args,
                        env=env,
                        namespace=namespace
                    )

                    if success:
                        logger.info(f"Successfully reconnected to {server_id}")
                    else:
                        logger.error(f"Failed to reconnect to {server_id}")

                except Exception as e:
                    logger.error(f"Error reconnecting to process-spawned server {server_id}: {e}")

            else:
                logger.warning(f"Unknown server configuration type for {server_id}, cannot reconnect")

        except Exception as e:
            logger.error(f"Error during reconnection to {server_id}: {e}")

    async def _ensure_third_party_connections(self):
        """Ensure third-party connections are established (lazy initialization)."""
        if not self.third_party_client or not self.third_party_client.connections:
            logger.info("Third-party connections not established, initializing...")
            await self.load_third_party_servers()

    async def _check_connection_health(self, server_id: str) -> bool:
        """Check if a connection to a server is healthy."""
        try:
            # Ensure connections are established first
            await self._ensure_third_party_connections()

            if not self.third_party_client:
                return False

            connection = self.third_party_client.connections.get(server_id)
            if not connection:
                return False

            client = connection.get('client')
            if not client:
                return False

            # Check if the client reports as connected
            if hasattr(client, 'connected'):
                if not client.connected:
                    return False

            # For SSE connections, try a simple validation
            if hasattr(client, 'session') and client.session:
                try:
                    # Try to list tools as a health check (with short timeout)
                    await asyncio.wait_for(client.session.list_tools(), timeout=5.0)
                    return True
                except Exception as e:
                    logger.warning(f"Connection health check failed for {server_id}: {e}")
                    return False

            # If no session attribute, assume it's healthy if it exists
            return True

        except Exception as e:
            logger.warning(f"Error checking connection health for {server_id}: {e}")
            return False

    async def _register_third_party_tools(self, server_id: str, namespace: str):
        """Register tools from a third-party server as delegated tools."""
        connection = self.third_party_client.connections.get(server_id)
        if not connection:
            return
        
        for tool in connection['tools']:
            original_tool_name = tool['name']
            # Use double underscore to clearly distinguish from local tools with single underscores
            namespaced_tool_name = f"{namespace}__{original_tool_name}"

            # Store tool mapping
            self.third_party_tools[namespaced_tool_name] = {
                'server_id': server_id,
                'original_name': original_tool_name,
                'schema': tool
            }

            # Debug: Log the tool schema
            logger.info(f"Registering tool {namespaced_tool_name} with schema: {tool.get('input_schema', 'No schema')}")

            # Create delegated tool function
            await self._create_delegated_tool(namespaced_tool_name, original_tool_name, server_id, tool)
    
    async def _create_delegated_tool(self, namespaced_name: str, original_name: str,
                                   server_id: str, tool_schema: Dict[str, Any]):
        """Create a delegated tool that proxies to a third-party server."""

        # Create a closure to capture the variables
        def create_tool_function():
            async def delegated_tool_func(ctx: Context, **kwargs) -> str:
                """Delegated tool function that proxies to third-party server."""
                try:
                    # Report progress
                    await ctx.report_progress(
                        progress=0,
                        total=3,
                        message=f"Delegating {original_name} to {server_id}"
                    )

                    # Call the third-party tool
                    # Handle argument unwrapping - sometimes args come wrapped in 'kwargs'
                    if 'kwargs' in kwargs and len(kwargs) == 1:
                        # Arguments are wrapped in a 'kwargs' object, unwrap them
                        tool_input = kwargs['kwargs']
                    else:
                        # Arguments are passed directly as keyword arguments
                        tool_input = kwargs

                    # Apply parameter mapping from config
                    if server_id in self.parameter_mappings:
                        tool_mappings = self.parameter_mappings[server_id].get(original_name, {})
                        for old_param, new_param in tool_mappings.items():
                            if old_param in tool_input and new_param not in tool_input:
                                tool_input[new_param] = tool_input.pop(old_param)
                                logger.info(f"Mapped '{old_param}' to '{new_param}' for {original_name}: {tool_input}")

                    # Apply default parameters from config
                    if server_id in self.default_parameters:
                        tool_defaults = self.default_parameters[server_id].get(original_name, {})
                        for param, default_value in tool_defaults.items():
                            if param not in tool_input:
                                tool_input[param] = default_value
                                logger.info(f"Applied default parameter '{param}': {default_value} for {original_name}")

                    # Special handling for Firecrawl tools - add required parameters
                    if original_name.startswith('firecrawl_'):
                        tool_input = tool_input.copy()

                        # Add default formats if missing
                        if 'formats' not in tool_input:
                            tool_input['formats'] = ['markdown']

                        # Add onlyMainContent for better performance and reliability
                        if 'onlyMainContent' not in tool_input:
                            tool_input['onlyMainContent'] = True

                        logger.info(f"Enhanced Firecrawl args for {original_name}: {tool_input}")

                    # Log the tool input for debugging
                    logger.info(f"Calling {original_name} with input: {tool_input}")

                    # Call the third-party tool via MultiMCPClient with better error handling
                    try:
                        # Add timeout handling for long-running tools like web scraping
                        timeout_seconds = 120  # 2 minutes for web scraping

                        # Try the tool call with retry logic for connection issues
                        result = await self._call_tool_with_retry(
                            server_id=server_id,
                            tool_name=original_name,
                            tool_input=tool_input,
                            tool_call_id=f"delegated_{namespaced_name}",
                            timeout_seconds=timeout_seconds
                        )

                        # Log the result for debugging
                        logger.info(f"Tool call result for {original_name}: success={result.get('success', False)}")
                        if result.get('success', False):
                            content = result.get('content')
                            logger.info(f"Tool call content type: {type(content)}, content preview: {str(content)[:200] if content else 'None'}")
                        else:
                            logger.error(f"Tool call failed: {result.get('error', 'Unknown error')}")

                    except asyncio.TimeoutError:
                        logger.error(f"Tool call {original_name} timed out after {timeout_seconds} seconds")
                        result = {
                            'success': False,
                            'error': f'Tool call timed out after {timeout_seconds} seconds',
                            'content': None
                        }
                    except Exception as delegation_error:
                        logger.error(f"Exception during tool delegation: {delegation_error}")
                        result = {
                            'success': False,
                            'error': f'Delegation exception: {str(delegation_error)}',
                            'content': None
                        }

                    await ctx.report_progress(
                        progress=2,
                        total=3,
                        message="Processing third-party response"
                    )

                    if result['success']:
                        await ctx.report_progress(
                            progress=3,
                            total=3,
                            message="Complete"
                        )

                        # Handle different content formats from third-party tools
                        content = result['content']
                        logger.info(f"Processing content for {original_name}: type={type(content)}, is_list={isinstance(content, list)}")

                        if isinstance(content, list) and len(content) > 0:
                            # If content is a list of objects (like Firecrawl returns)
                            logger.info(f"Content is list with {len(content)} items, first item type: {type(content[0])}")
                            if hasattr(content[0], 'text'):
                                # Handle TextContent objects
                                return content[0].text
                            elif isinstance(content[0], dict) and 'text' in content[0]:
                                # Handle dict format
                                return content[0]['text']
                            else:
                                # Return the first item as string
                                return str(content[0])
                        else:
                            # Return content as-is (for string content like Brave search)
                            logger.info(f"Returning content as-is: {str(content)[:100] if content else 'None'}...")
                            return str(content) if content is not None else "No content returned"
                    else:
                        error_msg = f"Third-party error from {server_id}: {result.get('error', 'Unknown error')}"
                        logger.error(f"Detailed error from {server_id}: {result}")
                        return error_msg

                except Exception as e:
                    error_msg = f"Delegation error calling {original_name} on {server_id}: {e}"
                    logger.error(error_msg)
                    return error_msg

            return delegated_tool_func

        # Create the tool function
        tool_func = create_tool_function()

        # Set the function name and description for FastMCP
        tool_func.__name__ = namespaced_name
        description = f"[{server_id}] {tool_schema.get('description', 'Third-party tool')}"
        tool_func.__doc__ = description

        try:
            # Register the delegated tool with FastMCP using add_tool
            # The issue is that FastMCP expects specific parameter schemas
            # Let's use a simpler approach and register it directly
            self.mcp.add_tool(tool_func)
            logger.info(f"Registered delegated tool: {namespaced_name} -> {server_id}.{original_name}")

        except Exception as e:
            logger.error(f"Error registering delegated tool {namespaced_name}: {e}")
            logger.error(f"Tool schema: {tool_schema}")
            # Continue with other tools instead of failing completely
            return

    async def initialize(self):
        """Initialize the server (load third-party servers)."""
        logger.info("Initializing enhanced MCP server...")

        # Load third-party servers with proper error isolation
        try:
            await self.load_third_party_servers()
        except Exception as e:
            logger.error(f"Error loading third-party servers: {e}")
            # Continue with local tools only
            logger.info("Continuing with local tools only")

        logger.info("Enhanced MCP server initialization complete")

    def start_server(self, port: int = 9000, host: str = "0.0.0.0"):
        """Start the enhanced MCP server using uvicorn."""
        logger.info(f"Starting enhanced MCP server on {host}:{port}")

        try:
            # Create the streamable HTTP app
            app = self.mcp.streamable_http_app()

            # Use uvicorn to run the server
            import uvicorn
            uvicorn.run(
                app,
                host=host,
                port=port,
                log_level="info",
                access_log=False
            )
        except KeyboardInterrupt:
            logger.info("Server interrupted by user")
        except Exception as e:
            logger.error(f"Server error: {e}")
            raise
    
    async def cleanup(self):
        """Clean up third-party connections with improved error handling."""
        if self.third_party_client:
            try:
                logger.info("Cleaning up third-party connections...")
                # Use timeout to prevent hanging during cleanup
                await asyncio.wait_for(self.third_party_client.cleanup(), timeout=5.0)
                logger.info("Third-party connections cleaned up successfully")
            except asyncio.TimeoutError:
                logger.warning("Third-party cleanup timeout (non-critical)")
            except Exception as e:
                logger.warning(f"Error during third-party cleanup (non-critical): {e}")
                # Don't re-raise, as this is cleanup code


def create_example_server_config():
    """Create an example configuration file using Augment pattern."""
    config = {
        "description": "Enhanced MCP Server Configuration (Augment Pattern)",
        "mcpServers": {
            "firecrawl-mcp": {
                "enabled": False,
                "command": "npx",
                "args": ["-y", "firecrawl-mcp"],
                "env": {
                    "FIRECRAWL_API_KEY": "fc-your-api-key-here"
                },
                "namespace": "web",
                "description": "Firecrawl MCP server spawned via npx"
            },
            "filesystem-mcp": {
                "enabled": False,
                "command": "npx",
                "args": ["@modelcontextprotocol/server-filesystem", "/path/to/directory"],
                "env": {},
                "namespace": "fs",
                "description": "Filesystem MCP server"
            },
            "python-mcp-example": {
                "enabled": False,
                "command": "uv",
                "args": ["run", "my-mcp-server.py"],
                "env": {
                    "API_KEY": "your-api-key"
                },
                "namespace": "custom",
                "description": "Custom Python MCP server"
            },
            "firecrawl-hosted": {
                "enabled": False,
                "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
                "protocol": "sse",
                "namespace": "web",
                "description": "Firecrawl hosted MCP server",
                "parameterMapping": {
                    "firecrawl_scrape": {},
                    "firecrawl_search": {
                        "q": "query"
                    }
                },
                "defaultParameters": {
                    "firecrawl_scrape": {
                        "formats": ["markdown"],
                        "onlyMainContent": True
                    }
                }
            }
        },
        "chat_term_usage": {
            "description": "Connect chat_term to this enhanced server",
            "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp",
            "available_tools": [
                "echostring (local)",
                "echostring_table (local)",
                "long_task (local)",
                "server_status (local)",
                "list_all_tools (local)",
                "third_party_health (local)",
                "web_firecrawl_scrape (delegated)",
                "fs_read_file (delegated)",
                "custom_my_tool (delegated)"
            ]
        }
    }
    
    config_file = "server_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created example server configuration: {config_file}")
    return config_file


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Enhanced MCP HTTP Server")
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--config", default="server_config.json", help="Configuration file")
    parser.add_argument("--create-config", action="store_true", help="Create example config and exit")

    args = parser.parse_args()

    if args.create_config:
        create_example_server_config()
        return

    # Create the enhanced server
    enhanced_server = EnhancedMCPServer(args.config)

    # Initialize third-party servers asynchronously
    async def initialize_server():
        try:
            await enhanced_server.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize server: {e}")
            return False
        return True

    # Run initialization with better isolation
    try:
        # Use a new event loop for initialization to avoid context conflicts
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(initialize_server())
            if not success:
                print("Failed to initialize enhanced server")
                return
        finally:
            # Clean shutdown of the initialization loop
            try:
                # Cancel any remaining tasks
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()

                # Wait for tasks to complete cancellation
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except Exception as cleanup_error:
                logger.warning(f"Initialization cleanup warning: {cleanup_error}")
            finally:
                loop.close()

    except KeyboardInterrupt:
        print("Initialization interrupted by user")
        return
    except Exception as e:
        print(f"Initialization error: {e}")
        logger.error(f"Detailed initialization error: {e}", exc_info=True)
        return

    # Setup signal handlers for graceful shutdown
    shutdown_event = asyncio.Event()

    def signal_handler(signum, _frame):
        print(f"\nReceived signal {signum}, initiating graceful shutdown...")
        shutdown_event.set()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Start the server (synchronous)
    try:
        print(f"Enhanced MCP server starting on {args.host}:{args.port}")
        print("Press Ctrl+C to stop the server gracefully")
        enhanced_server.start_server(args.port, args.host)
    except KeyboardInterrupt:
        print("\nShutting down enhanced server...")
    except Exception as e:
        print(f"Server error: {e}")
        logger.error(f"Server error details: {e}", exc_info=True)
    finally:
        # Cleanup third-party connections
        print("Cleaning up third-party connections...")

        async def cleanup():
            try:
                await enhanced_server.cleanup()
            except Exception as e:
                logger.warning(f"Cleanup warning: {e}")

        try:
            asyncio.run(cleanup())
        except Exception as e:
            logger.warning(f"Final cleanup warning: {e}")

        print("Enhanced server shutdown complete.")


if __name__ == "__main__":
    main()
