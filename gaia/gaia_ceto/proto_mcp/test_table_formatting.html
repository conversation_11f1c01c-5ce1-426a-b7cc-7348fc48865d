<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Formatting Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .json-output {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MCP Table Formatting Test</h1>
        <p>This page demonstrates how the echostring_table tool output is formatted in the web UI.</p>
        
        <div class="test-section">
            <h3>Test 1: Basic Table</h3>
            <button onclick="testBasicTable()">Generate Basic Table</button>
            <div id="basic-table-json" class="json-output"></div>
            <div id="basic-table-html"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Table with Different Data</h3>
            <button onclick="testDataTable()">Generate Data Table</button>
            <div id="data-table-json" class="json-output"></div>
            <div id="data-table-html"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Mixed Content (Table + Text)</h3>
            <button onclick="testMixedContent()">Generate Mixed Content</button>
            <div id="mixed-content-json" class="json-output"></div>
            <div id="mixed-content-html"></div>
        </div>
    </div>

    <script>
        // Simulate the formatMessage function from chat_app.html
        function isTableData(content) {
            try {
                const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                if (jsonMatch) {
                    const tableData = JSON.parse(jsonMatch[0]);
                    return tableData.type === 'table' && tableData.headers && tableData.rows;
                }
                return false;
            } catch (e) {
                return false;
            }
        }

        function formatTableData(content) {
            try {
                const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                if (!jsonMatch) {
                    return content;
                }

                const tableData = JSON.parse(jsonMatch[0]);
                
                let html = '';
                
                if (tableData.title) {
                    html += `<h4 style="margin-bottom: 10px; color: #333;">${tableData.title}</h4>`;
                }
                
                html += '<table style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #ddd;">';
                
                if (tableData.headers && tableData.headers.length > 0) {
                    html += '<thead><tr>';
                    tableData.headers.forEach(header => {
                        html += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: left; font-weight: bold;">${header}</th>`;
                    });
                    html += '</tr></thead>';
                }
                
                if (tableData.rows && tableData.rows.length > 0) {
                    html += '<tbody>';
                    tableData.rows.forEach((row, rowIndex) => {
                        const bgColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                        html += `<tr style="background-color: ${bgColor};">`;
                        row.forEach(cell => {
                            html += `<td style="border: 1px solid #ddd; padding: 8px;">${cell}</td>`;
                        });
                        html += '</tr>';
                    });
                    html += '</tbody>';
                }
                
                html += '</table>';
                
                const remainingContent = content.replace(jsonMatch[0], '').trim();
                if (remainingContent) {
                    html += `<p>${remainingContent}</p>`;
                }
                
                return html;
            } catch (e) {
                console.error('Error formatting table data:', e);
                return content;
            }
        }

        function testBasicTable() {
            const tableData = {
                "type": "table",
                "headers": ["Column 1", "Column 2", "Column 3"],
                "rows": [
                    ["Hello", "Hello_modified", "Hello_final"],
                    ["Row 2 Data", "More data", "Even more"],
                    ["Sample", "Table", "Content"],
                    ["Echo: Hello", "Repeated", "Again"]
                ],
                "title": "Echo Table for: Hello"
            };
            
            const jsonString = JSON.stringify(tableData);
            document.getElementById('basic-table-json').textContent = jsonString;
            document.getElementById('basic-table-html').innerHTML = formatTableData(jsonString);
        }

        function testDataTable() {
            const tableData = {
                "type": "table",
                "headers": ["Company", "Funding", "Stage"],
                "rows": [
                    ["AgFunder", "$50M", "Series B"],
                    ["TechCorp", "$25M", "Series A"],
                    ["StartupXYZ", "$5M", "Seed"],
                    ["InnovateCo", "$100M", "Series C"]
                ],
                "title": "Sample Company Data"
            };
            
            const jsonString = JSON.stringify(tableData);
            document.getElementById('data-table-json').textContent = jsonString;
            document.getElementById('data-table-html').innerHTML = formatTableData(jsonString);
        }

        function testMixedContent() {
            const tableData = {
                "type": "table",
                "headers": ["Metric", "Value", "Change"],
                "rows": [
                    ["Revenue", "$1.2M", "+15%"],
                    ["Users", "50K", "+25%"],
                    ["Growth Rate", "12%", "+3%"]
                ],
                "title": "Q4 Performance Metrics"
            };
            
            const content = `Here are the latest performance metrics: ${JSON.stringify(tableData)} The data shows strong growth across all key indicators.`;
            document.getElementById('mixed-content-json').textContent = content;
            document.getElementById('mixed-content-html').innerHTML = formatTableData(content);
        }

        // Auto-run the first test
        window.onload = function() {
            testBasicTable();
        };
    </script>
</body>
</html>
