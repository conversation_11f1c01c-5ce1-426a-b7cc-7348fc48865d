#!/usr/bin/env python3
"""
Test script for the echostring_table tool

This script tests the new echostring_table tool to ensure it returns
properly formatted JSON table data.
"""

import asyncio
import json
from mcp_tools import echostring_table


async def test_echostring_table():
    """Test the echostring_table tool"""
    print("Testing echostring_table tool...")
    
    # Test with a simple phrase
    test_phrase = "Hello World"
    result = await echostring_table(test_phrase)
    
    print(f"\nInput phrase: {test_phrase}")
    print(f"Raw result: {result}")
    
    # Parse the JSON to verify it's valid
    try:
        table_data = json.loads(result)
        print(f"\nParsed JSON:")
        print(f"Type: {table_data.get('type')}")
        print(f"Title: {table_data.get('title')}")
        print(f"Headers: {table_data.get('headers')}")
        print(f"Number of rows: {len(table_data.get('rows', []))}")
        
        # Pretty print the table data
        print(f"\nFormatted table data:")
        print(json.dumps(table_data, indent=2))
        
        # Verify the structure
        assert table_data['type'] == 'table', "Type should be 'table'"
        assert 'headers' in table_data, "Should have headers"
        assert 'rows' in table_data, "Should have rows"
        assert 'title' in table_data, "Should have title"
        assert len(table_data['headers']) > 0, "Should have at least one header"
        assert len(table_data['rows']) > 0, "Should have at least one row"
        
        print("\n✅ All tests passed!")
        
    except json.JSONDecodeError as e:
        print(f"\n❌ Error: Result is not valid JSON: {e}")
        return False
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False
    
    return True


async def test_multiple_phrases():
    """Test with multiple different phrases"""
    test_phrases = [
        "Test",
        "Multiple Words Here",
        "Special-Characters_123",
        "A very long phrase with many words to test how it handles longer input"
    ]
    
    print("\n" + "="*50)
    print("Testing multiple phrases...")
    
    for phrase in test_phrases:
        print(f"\nTesting phrase: '{phrase}'")
        result = await echostring_table(phrase)
        
        try:
            table_data = json.loads(result)
            print(f"✅ Valid JSON returned for '{phrase}'")
            print(f"   Title: {table_data.get('title')}")
            print(f"   First row, first cell: {table_data['rows'][0][0] if table_data.get('rows') else 'N/A'}")
        except Exception as e:
            print(f"❌ Error with phrase '{phrase}': {e}")


if __name__ == "__main__":
    print("MCP Tools - echostring_table Test")
    print("=" * 40)
    
    # Run the tests
    asyncio.run(test_echostring_table())
    asyncio.run(test_multiple_phrases())
    
    print("\n" + "="*50)
    print("Test completed!")
